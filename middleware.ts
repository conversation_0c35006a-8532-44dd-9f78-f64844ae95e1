// middleware.ts
import { decrypt } from '@/src/lib/session';
import { NextRequest, NextResponse } from 'next/server';

// Define protected and public routes
const protectedRoutes = [
  '/dashboard',
  '/student-management',
  '/staff-management',
  '/academic-management',
  '/attendance-management',
  '/fee-management',
  '/reports',
  '/settings'
];

const publicRoutes = [
  '/',
  '/product',
  '/resources',
  '/auth'
];

export default async function middleware(req: NextRequest) {
  // Get the pathname from the request URL
  const path = req.nextUrl.pathname;
  
  // Check if the current route is protected or public
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route));
  const isPublicRoute = publicRoutes.some(route => path === route || path.startsWith(route + '/'));
  
  // Get session from cookie
  const cookie = req.cookies.get('session')?.value;
  const session = await decrypt(cookie);
  
  // Redirect to auth page if trying to access protected route without session
  if (isProtectedRoute && !session?.userId) {
    return NextResponse.redirect(new URL('/auth', req.nextUrl));
  }
  
  // Redirect to dashboard if authenticated user tries to access auth page
  if (path === '/auth' && session?.userId) {
    return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
  }
  
  // Check if session is expired with grace period
  if (session?.expiresAt) {
    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const gracePeriod = 5 * 60 * 1000; // 5 minutes grace period

    // Only redirect if session is expired beyond grace period
    if (currentTime > new Date(expirationTime.getTime() + gracePeriod)) {
      // Clear the expired session cookie
      const response = NextResponse.redirect(new URL('/auth', req.nextUrl));
      response.cookies.delete('session');
      return response;
    }
  }
  
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)' 
  ],
};
