'use client';

import { useState } from 'react';

export default function FeeManagementPage() {
  const [selectedYear, setSelectedYear] = useState('2023-2024');

  return (
    <div className="space-y-3">
     
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-9 gap-3">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-7 space-y-3">
          {/* Financial Overview Cards */}
          <div className="bg-white rounded-2xl shadow-2xl border-2 border-blue-500 p-4 hover:shadow-3xl transition-all duration-500 relative">
            {/* Content */}
            <div className="relative z-10">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Pending Collections */}
                <div className="text-center group">
                  <div className="relative mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 border border-blue-200/50">
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-400/20 to-blue-600/20 group-hover:from-blue-400/30 group-hover:to-blue-600/30 transition-all duration-300"></div>
                      <svg className="w-5 h-5 text-blue-600 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute -inset-1 rounded-xl bg-gradient-to-br from-blue-400/10 to-blue-600/10 blur-sm group-hover:blur-md transition-all duration-300"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-slate-500 font-medium tracking-wide uppercase">Pending Collections</div>
                    <div className="text-xl font-bold text-slate-900 tracking-tight">$269,500</div>
                    <div className="text-xs text-slate-400 font-medium">↑ 12% from last month</div>
                  </div>
                </div>

                {/* Total Collected */}
                <div className="text-center group">
                  <div className="relative mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-50 to-green-100 rounded-xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 border border-green-200/50">
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-green-400/20 to-green-600/20 group-hover:from-green-400/30 group-hover:to-green-600/30 transition-all duration-300"></div>
                      <svg className="w-5 h-5 text-green-600 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute -inset-1 rounded-xl bg-gradient-to-br from-green-400/10 to-green-600/10 blur-sm group-hover:blur-md transition-all duration-300"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-slate-500 font-medium tracking-wide uppercase">Total Collected</div>
                    <div className="text-xl font-bold text-slate-900 tracking-tight">$980,500</div>
                    <div className="text-xs text-slate-400 font-medium">↑ 8% from last month</div>
                  </div>
                </div>

                {/* Defaulters */}
                <div className="text-center group">
                  <div className="relative mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-50 to-red-100 rounded-xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 border border-red-200/50">
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-red-400/20 to-red-600/20 group-hover:from-red-400/30 group-hover:to-red-600/30 transition-all duration-300"></div>
                      <svg className="w-5 h-5 text-red-600 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div className="absolute -inset-1 rounded-xl bg-gradient-to-br from-red-400/10 to-red-600/10 blur-sm group-hover:blur-md transition-all duration-300"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-slate-500 font-medium tracking-wide uppercase">Defaulters</div>
                    <div className="text-xl font-bold text-red-600 tracking-tight flex items-center justify-center space-x-1">
                      <span>12</span>
                      <span className="text-sm text-red-500 font-medium">Students</span>
                    </div>
                    <div className="text-xs text-slate-400 font-medium">↓ 3% from last month</div>
                  </div>
                </div>

                {/* Collection Rate */}
                <div className="text-center group">
                  <div className="relative mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-teal-50 to-teal-100 rounded-xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 border border-teal-200/50">
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-teal-400/20 to-teal-600/20 group-hover:from-teal-400/30 group-hover:to-teal-600/30 transition-all duration-300"></div>
                      <svg className="w-5 h-5 text-teal-600 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <div className="absolute -inset-1 rounded-xl bg-gradient-to-br from-teal-400/10 to-teal-600/10 blur-sm group-hover:blur-md transition-all duration-300"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-slate-500 font-medium tracking-wide uppercase">Collection Rate</div>
                    <div className="text-xl font-bold text-teal-600 tracking-tight">78.2%</div>
                    <div className="text-xs text-slate-400 font-medium">↑ 5% from last month</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fee Collection Analysis Chart */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-4 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg p-2.5 border border-blue-200/60 shadow-sm leading-tight">
              <h3 className="text-base font-semibold text-slate-900 tracking-tight leading-tight">Fee Collection Analysis</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm-app text-slate-600 font-medium">Year:</span>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="text-sm-app border border-slate-300 rounded-lg px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <option value="2023-2024">2023-2024</option>
                  <option value="2022-2023">2022-2023</option>
                  <option value="2021-2022">2021-2022</option>
                </select>
              </div>
            </div>

            {/* Chart Area - Professional Line Chart */}
            <div className="h-64 bg-gradient-to-br from-slate-50 to-white rounded-xl p-4 border border-slate-200/50 relative">
              {/* Chart Container */}
              <div className="relative h-full w-full">
                <svg className="w-full h-full" viewBox="0 0 900 280" preserveAspectRatio="none">
                  <defs>
                    {/* Professional Color Gradients */}
                    <linearGradient id="collectedGradientPro" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{stopColor:'#1e40af', stopOpacity:0.4}} />
                      <stop offset="50%" style={{stopColor:'#3b82f6', stopOpacity:0.2}} />
                      <stop offset="100%" style={{stopColor:'#93c5fd', stopOpacity:0.05}} />
                    </linearGradient>
                    <linearGradient id="pendingGradientPro" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{stopColor:'#0891b2', stopOpacity:0.4}} />
                      <stop offset="50%" style={{stopColor:'#06b6d4', stopOpacity:0.2}} />
                      <stop offset="100%" style={{stopColor:'#67e8f9', stopOpacity:0.05}} />
                    </linearGradient>
                    <linearGradient id="overdueGradientPro" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{stopColor:'#7c3aed', stopOpacity:0.4}} />
                      <stop offset="50%" style={{stopColor:'#8b5cf6', stopOpacity:0.2}} />
                      <stop offset="100%" style={{stopColor:'#c4b5fd', stopOpacity:0.05}} />
                    </linearGradient>
                    
                    {/* Professional Grid Pattern */}
                    <pattern id="professionalGrid" width="75" height="46" patternUnits="userSpaceOnUse">
                      <path d="M 75 0 L 0 0 0 46" fill="none" stroke="#e2e8f0" strokeWidth="0.8" opacity="0.3"/>
                    </pattern>
                    
                    {/* Drop Shadow Filter */}
                    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
                      <feDropShadow dx="1" dy="1" stdDeviation="2" floodOpacity="0.15"/>
                    </filter>
                    
                    {/* Glow Filter */}
                    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
                      <feMerge> 
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                      </feMerge>
                    </filter>
                  </defs>
                  
                  {/* Grid Background */}
                  <rect width="100%" height="100%" fill="url(#professionalGrid)" />
                  
                  {/* Y-Axis Labels */}
                  <g className="text-xs fill-slate-500">
                    <text x="30" y="45" textAnchor="middle" className="text-xs font-medium">120k</text>
                    <text x="30" y="90" textAnchor="middle" className="text-xs font-medium">90k</text>
                    <text x="30" y="135" textAnchor="middle" className="text-xs font-medium">60k</text>
                    <text x="30" y="180" textAnchor="middle" className="text-xs font-medium">30k</text>
                    <text x="30" y="225" textAnchor="middle" className="text-xs font-medium">0k</text>
                  </g>
                  
                  {/* Chart Data */}
                  {(() => {
                    const data = [
                      { month: 'Jan', collected: 85, pending: 35, overdue: 15 },
                      { month: 'Feb', collected: 75, pending: 45, overdue: 25 },
                      { month: 'Mar', collected: 95, pending: 25, overdue: 8 },
                      { month: 'Apr', collected: 88, pending: 35, overdue: 18 },
                      { month: 'May', collected: 70, pending: 55, overdue: 30 },
                      { month: 'Jun', collected: 78, pending: 45, overdue: 22 },
                      { month: 'Jul', collected: 65, pending: 65, overdue: 35 },
                      { month: 'Aug', collected: 88, pending: 35, overdue: 20 },
                      { month: 'Sep', collected: 95, pending: 25, overdue: 12 },
                      { month: 'Oct', collected: 105, pending: 20, overdue: 8 },
                      { month: 'Nov', collected: 115, pending: 15, overdue: 5 },
                      { month: 'Dec', collected: 125, pending: 10, overdue: 3 }
                    ];
                    
                    const maxValue = 130;
                    const chartPadding = 45;
                    const chartWidth = 900 - chartPadding * 2;
                    const chartHeight = 280 - chartPadding * 2;
                    
                    const points = data.map((d, i) => ({
                      x: chartPadding + (i * chartWidth) / (data.length - 1),
                      collected: chartPadding + chartHeight - (d.collected / maxValue) * chartHeight,
                      pending: chartPadding + chartHeight - (d.pending / maxValue) * chartHeight,
                      overdue: chartPadding + chartHeight - (d.overdue / maxValue) * chartHeight
                    }));
                    
                    const createPath = (points: any[], key: string) => 
                      `M ${points.map(p => `${p.x},${p[key]}`).join(' L ')}`;
                    
                    const createAreaPath = (points: any[], key: string) => 
                      `M ${points[0].x},${chartPadding + chartHeight} L ${points.map(p => `${p.x},${p[key]}`).join(' L ')} L ${points[points.length-1].x},${chartPadding + chartHeight} Z`;
                    
                    return (
                      <>
                        {/* Area fills with gradients */}
                        <path d={createAreaPath(points, 'collected')} fill="url(#collectedGradientPro)" />
                        <path d={createAreaPath(points, 'pending')} fill="url(#pendingGradientPro)" />
                        <path d={createAreaPath(points, 'overdue')} fill="url(#overdueGradientPro)" />
                        
                        {/* Professional Lines with shadows */}
                        <path d={createPath(points, 'collected')} 
                              fill="none" 
                              stroke="#1e40af" 
                              strokeWidth="3.5" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              filter="url(#dropShadow)" />
                        <path d={createPath(points, 'pending')} 
                              fill="none" 
                              stroke="#0891b2" 
                              strokeWidth="3" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              filter="url(#dropShadow)" />
                        <path d={createPath(points, 'overdue')} 
                              fill="none" 
                              stroke="#7c3aed" 
                              strokeWidth="3" 
                              strokeLinecap="round" 
                              strokeLinejoin="round"
                              filter="url(#dropShadow)" />
                        
                        {/* Enhanced Data points with multiple layers */}
                        {points.map((point, i) => (
                          <g key={i}>
                            {/* Collected points - Deep Blue */}
                            <circle cx={point.x} cy={point.collected} r="8" fill="#bfdbfe" opacity="0.6" />
                            <circle cx={point.x} cy={point.collected} r="5" fill="#1e40af" stroke="white" strokeWidth="2.5" filter="url(#glow)" />
                            <circle cx={point.x} cy={point.collected} r="2" fill="white" />
                            
                            {/* Pending points - Teal */}
                            <circle cx={point.x} cy={point.pending} r="7" fill="#a7f3d0" opacity="0.6" />
                            <circle cx={point.x} cy={point.pending} r="4" fill="#0891b2" stroke="white" strokeWidth="2" filter="url(#glow)" />
                            <circle cx={point.x} cy={point.pending} r="1.5" fill="white" />
                            
                            {/* Overdue points - Purple */}
                            <circle cx={point.x} cy={point.overdue} r="7" fill="#ddd6fe" opacity="0.6" />
                            <circle cx={point.x} cy={point.overdue} r="4" fill="#7c3aed" stroke="white" strokeWidth="2" filter="url(#glow)" />
                            <circle cx={point.x} cy={point.overdue} r="1.5" fill="white" />
                          </g>
                        ))}
                        
                        {/* Month labels with better positioning */}
                        {data.map((d, i) => (
                          <text key={i} 
                                x={chartPadding + (i * chartWidth) / (data.length - 1)} 
                                y={chartPadding + chartHeight + 25} 
                                textAnchor="middle" 
                                className="text-xs font-semibold fill-slate-600">
                            {d.month}
                          </text>
                        ))}
                      </>
                    );
                  })()}
                </svg>
              </div>
            </div>

            {/* Chart Legend */}
            <div className="flex items-center justify-center space-x-8 mt-6 bg-gradient-to-r from-slate-50 to-white rounded-lg p-3 border border-slate-200/50 shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-4 h-4 bg-gradient-to-br from-blue-600 to-blue-800 rounded-full shadow-md"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-blue-400 rounded-full animate-pulse opacity-30"></div>
                </div>
                <span className="text-sm font-semibold text-slate-700">Collected</span>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">$980K</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-4 h-4 bg-gradient-to-br from-cyan-600 to-cyan-800 rounded-full shadow-md"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-cyan-400 rounded-full animate-pulse opacity-30"></div>
                </div>
                <span className="text-sm font-semibold text-slate-700">Pending</span>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">$269K</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-4 h-4 bg-gradient-to-br from-purple-600 to-purple-800 rounded-full shadow-md"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-purple-400 rounded-full animate-pulse opacity-30"></div>
                </div>
                <span className="text-sm font-semibold text-slate-700">Outstanding</span>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full">$45K</span>
              </div>
            </div>
          </div>

          {/* Fee Structure Breakdown */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-4 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-2.5 border border-purple-200/60 shadow-sm leading-tight">
              <h3 className="text-base font-semibold text-slate-900 tracking-tight leading-tight">Fee Structure Breakdown</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm-app text-slate-600 font-medium">View Class:</span>
                <select className="text-sm-app border border-slate-300 rounded-lg px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm hover:shadow-md transition-shadow duration-200">
                  <option value="class-10">Class 10</option>
                  <option value="class-9">Class 9</option>
                  <option value="class-11">Class 11</option>
                  <option value="class-12">Class 12</option>
                </select>
              </div>
            </div>

            {/* Fee Structure Table */}
            <div className="overflow-x-auto bg-white rounded-lg shadow-sm border border-slate-200/50">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-slate-600 to-slate-700">
                  <tr className="border-b border-slate-500">
                    <th className="text-left text-xs-app font-semibold text-white uppercase tracking-wider py-3 px-4">Fee Category</th>
                    <th className="text-left text-xs-app font-semibold text-white uppercase tracking-wider py-3 px-4">Amount (₹)</th>
                    <th className="text-left text-xs-app font-semibold text-white uppercase tracking-wider py-3 px-4">Frequency</th>
                    <th className="text-left text-xs-app font-semibold text-white uppercase tracking-wider py-3 px-4">Notes</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  <tr className="hover:bg-blue-50 transition-colors duration-150" style={{backgroundColor: '#f8fafc'}}>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-medium">Tuition Fee</td>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-semibold">₹15,000</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Monthly</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Core academic fee</td>
                  </tr>
                  <tr className="hover:bg-slate-50 transition-colors duration-150" style={{backgroundColor: '#eff6ff'}}>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-medium">Lab Fee</td>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-semibold">₹2,500</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Quarterly</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Science lab usage</td>
                  </tr>
                  <tr className="hover:bg-blue-50 transition-colors duration-150" style={{backgroundColor: '#f8fafc'}}>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-medium">Library Fee</td>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-semibold">₹1,000</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Annual</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Books and resources</td>
                  </tr>
                  <tr className="hover:bg-slate-50 transition-colors duration-150" style={{backgroundColor: '#eff6ff'}}>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-medium">Sports Fee</td>
                    <td className="py-3 px-4 text-sm-app text-slate-900 font-semibold">₹800</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Annual</td>
                    <td className="py-3 px-4 text-sm-app text-slate-600">Sports activities</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="lg:col-span-2 space-y-4">
          {/* Quick Actions */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-4 hover:shadow-xl transition-all duration-300 shadow-blue-200/40 hover:shadow-blue-300/60">
            <h3 className="text-base font-semibold text-slate-900 mb-4 tracking-tight">Quick Actions</h3>
            <div className="space-y-4">
              <button className="w-full bg-gradient-to-r from-blue-200 to-blue-300 hover:from-blue-300 hover:to-blue-400 text-blue-800 text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Generate New Invoice</span>
              </button>

              <button className="w-full bg-gradient-to-r from-green-200 to-green-300 hover:from-green-300 hover:to-green-400 text-green-800 text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Send Payment Reminders</span>
              </button>

              <button className="w-full bg-gradient-to-r from-purple-200 to-purple-300 hover:from-purple-300 hover:to-purple-400 text-purple-800 text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>View Collection Reports</span>
              </button>
            </div>
          </div>

          {/* Payment Collection Progress */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-4 hover:shadow-xl transition-all duration-300 shadow-green-200/40 hover:shadow-green-300/60">
            <h3 className="text-base font-semibold text-slate-900 mb-4 tracking-tight">Payment Collection Progress</h3>
            <div className="space-y-4">
              {/* Grade 10 - Section A */}
              <div className="group">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700 font-medium">Grade 10 - Section A</span>
                  <span className="text-sm-app font-semibold text-green-600">85%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2 shadow-inner">
                  <div className="bg-gradient-to-r from-green-200 to-green-400 h-2 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '85%' }}></div>
                </div>
              </div>

              {/* Grade 9 - Section B */}
              <div className="group">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700 font-medium">Grade 9 - Section B</span>
                  <span className="text-sm-app font-semibold text-blue-600">72%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2 shadow-inner">
                  <div className="bg-gradient-to-r from-blue-200 to-blue-400 h-2 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '72%' }}></div>
                </div>
              </div>

              {/* Grade 11 - Science */}
              <div className="group">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700 font-medium">Grade 11 - Science</span>
                  <span className="text-sm-app font-semibold text-orange-600">55%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2 shadow-inner">
                  <div className="bg-gradient-to-r from-orange-200 to-orange-400 h-2 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '55%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Outstanding Fees */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-3 hover:shadow-xl transition-all duration-300 shadow-red-200/40 hover:shadow-red-300/60">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-base font-semibold text-slate-900 tracking-tight">Outstanding Fees</h3>
              <button className="text-sm-app text-blue-600 hover:text-blue-700 font-medium hover:underline transition-all duration-200">View Details →</button>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-2.5 border border-red-200/50 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm-app text-slate-700 font-medium">Grade 12</div>
                  <div className="text-xs-app text-slate-500 font-medium">22 Students Outstanding</div>
                </div>
                <div className="text-lg-app font-bold text-red-600 tracking-tight">$15,250</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
