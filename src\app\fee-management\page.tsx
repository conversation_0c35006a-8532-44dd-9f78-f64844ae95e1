// src/app/fee-management/page.tsx
'use client';

import { useState } from 'react';

export default function FeeManagementPage() {
  const [selectedYear, setSelectedYear] = useState('2023-2024');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Fee Management</h1>
          <p className="text-sm-app text-slate-600 mt-1">
            Overview of institutional finances and fee collection.
          </p>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Financial Overview Cards */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Pending Collections */}
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-1">Pending Collections</div>
                <div className="text-xl-app font-bold text-slate-900">$269,500</div>
              </div>

              {/* Total Collected */}
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-1">Total Collected</div>
                <div className="text-xl-app font-bold text-slate-900">$980,500</div>
              </div>

              {/* Defaulters */}
              <div className="text-center">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-1">Defaulters</div>
                <div className="text-xl-app font-bold text-red-600">12</div>
                <div className="text-xs-app text-red-500">Students</div>
              </div>

              {/* Collection Rate */}
              <div className="text-center">
                <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-1">Collection Rate</div>
                <div className="text-xl-app font-bold text-teal-600">78.2%</div>
              </div>
            </div>
          </div>

          {/* Fee Collection Analysis Chart */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg-app font-semibold text-slate-900">Fee Collection Analysis</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm-app text-slate-600">Year:</span>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="text-sm-app border border-slate-300 rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="2023-2024">2023-2024</option>
                  <option value="2022-2023">2022-2023</option>
                  <option value="2021-2022">2021-2022</option>
                </select>
              </div>
            </div>

            {/* Chart Area */}
            <div className="h-64 bg-slate-50 rounded-lg flex items-end justify-between p-4 space-x-2">
              {[
                { month: 'Jan', collected: 60, pending: 20, overdue: 5 },
                { month: 'Feb', collected: 50, pending: 25, overdue: 8 },
                { month: 'Mar', collected: 70, pending: 15, overdue: 3 },
                { month: 'Apr', collected: 65, pending: 20, overdue: 6 },
                { month: 'May', collected: 45, pending: 30, overdue: 10 },
                { month: 'Jun', collected: 50, pending: 25, overdue: 8 },
                { month: 'Jul', collected: 35, pending: 35, overdue: 12 },
                { month: 'Aug', collected: 65, pending: 20, overdue: 7 },
                { month: 'Sep', collected: 75, pending: 15, overdue: 4 },
                { month: 'Oct', collected: 85, pending: 10, overdue: 2 },
                { month: 'Nov', collected: 95, pending: 8, overdue: 1 },
                { month: 'Dec', collected: 105, pending: 5, overdue: 1 },
              ].map((data, index) => (
                <div key={index} className="flex flex-col items-center space-y-2 flex-1">
                  <div className="flex flex-col items-center space-y-1 w-full">
                    {/* Collected */}
                    <div
                      className="bg-blue-500 rounded-t w-full"
                      style={{ height: `${data.collected}px` }}
                    ></div>
                    {/* Pending */}
                    <div
                      className="bg-blue-300 w-full"
                      style={{ height: `${data.pending}px` }}
                    ></div>
                    {/* Overdue */}
                    <div
                      className="bg-red-400 rounded-b w-full"
                      style={{ height: `${data.overdue}px` }}
                    ></div>
                  </div>
                  <span className="text-xs-app text-slate-600">{data.month}</span>
                </div>
              ))}
            </div>

            {/* Chart Legend */}
            <div className="flex items-center justify-center space-x-6 mt-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded"></div>
                <span className="text-xs-app text-slate-600">Collected</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-300 rounded"></div>
                <span className="text-xs-app text-slate-600">Pending</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-400 rounded"></div>
                <span className="text-xs-app text-slate-600">Overdue</span>
              </div>
            </div>
          </div>

          {/* Fee Structure Breakdown */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg-app font-semibold text-slate-900">Fee Structure Breakdown</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm-app text-slate-600">View Class:</span>
                <select className="text-sm-app border border-slate-300 rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="class-10">Class 10</option>
                  <option value="class-9">Class 9</option>
                  <option value="class-11">Class 11</option>
                  <option value="class-12">Class 12</option>
                </select>
              </div>
            </div>

            {/* Fee Structure Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="text-left text-xs-app font-medium text-slate-500 uppercase tracking-wider py-3">Fee Category</th>
                    <th className="text-left text-xs-app font-medium text-slate-500 uppercase tracking-wider py-3">Amount (₹)</th>
                    <th className="text-left text-xs-app font-medium text-slate-500 uppercase tracking-wider py-3">Frequency</th>
                    <th className="text-left text-xs-app font-medium text-slate-500 uppercase tracking-wider py-3">Notes</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  <tr>
                    <td className="py-3 text-sm-app text-slate-900">Tuition Fee</td>
                    <td className="py-3 text-sm-app text-slate-900">₹15,000</td>
                    <td className="py-3 text-sm-app text-slate-600">Monthly</td>
                    <td className="py-3 text-sm-app text-slate-600">Core academic fee</td>
                  </tr>
                  <tr>
                    <td className="py-3 text-sm-app text-slate-900">Lab Fee</td>
                    <td className="py-3 text-sm-app text-slate-900">₹2,500</td>
                    <td className="py-3 text-sm-app text-slate-600">Quarterly</td>
                    <td className="py-3 text-sm-app text-slate-600">Science lab usage</td>
                  </tr>
                  <tr>
                    <td className="py-3 text-sm-app text-slate-900">Library Fee</td>
                    <td className="py-3 text-sm-app text-slate-900">₹1,000</td>
                    <td className="py-3 text-sm-app text-slate-600">Annual</td>
                    <td className="py-3 text-sm-app text-slate-600">Books and resources</td>
                  </tr>
                  <tr>
                    <td className="py-3 text-sm-app text-slate-900">Sports Fee</td>
                    <td className="py-3 text-sm-app text-slate-900">₹800</td>
                    <td className="py-3 text-sm-app text-slate-600">Annual</td>
                    <td className="py-3 text-sm-app text-slate-600">Sports activities</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 className="text-lg-app font-semibold text-slate-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Generate New Invoice</span>
              </button>

              <button className="w-full bg-green-500 hover:bg-green-600 text-white text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Send Payment Reminders</span>
              </button>

              <button className="w-full bg-slate-600 hover:bg-slate-700 text-white text-sm-app font-medium py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>View Collection Reports</span>
              </button>
            </div>
          </div>

          {/* Payment Collection Progress */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 className="text-lg-app font-semibold text-slate-900 mb-4">Payment Collection Progress</h3>
            <div className="space-y-4">
              {/* Grade 10 - Section A */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700">Grade 10 - Section A</span>
                  <span className="text-sm-app font-medium text-green-600">85%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>

              {/* Grade 9 - Section B */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700">Grade 9 - Section B</span>
                  <span className="text-sm-app font-medium text-blue-600">72%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '72%' }}></div>
                </div>
              </div>

              {/* Grade 11 - Science */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm-app text-slate-700">Grade 11 - Science</span>
                  <span className="text-sm-app font-medium text-orange-600">55%</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full" style={{ width: '55%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Outstanding Fees */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg-app font-semibold text-slate-900">Outstanding Fees</h3>
              <button className="text-sm-app text-blue-600 hover:text-blue-700">View Details →</button>
            </div>

            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm-app text-slate-700">Grade 12</div>
                  <div className="text-xs-app text-slate-500">22 Students Outstanding</div>
                </div>
                <div className="text-lg-app font-bold text-red-600">$15,250</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
