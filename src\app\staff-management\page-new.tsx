// src/app/staff-management/page.tsx
'use client';

import { useState } from 'react';

export default function StaffManagementPage() {
  const [activeView, setActiveView] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-6">
      {/* Floating Header with Glass Effect */}
      <div className="mb-8 backdrop-blur-xl bg-white/30 border border-white/20 rounded-3xl p-6 shadow-2xl">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          {/* Hero Section */}
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Staff Hub
              </h1>
              <p className="text-gray-600 font-medium">Manage your team with style</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            <button className="group relative overflow-hidden bg-gradient-to-r from-emerald-400 to-cyan-400 hover:from-emerald-500 hover:to-cyan-500 text-white font-semibold py-3 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105">
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                <span>Import Staff</span>
              </div>
            </button>
            
            <button className="group relative overflow-hidden bg-gradient-to-r from-violet-400 to-purple-400 hover:from-violet-500 hover:to-purple-500 text-white font-semibold py-3 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105">
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                <span>Add Member</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-12 gap-8">
        {/* Left Section - Overview Cards */}
        <div className="xl:col-span-8 space-y-8">
          {/* Stats Cards with Modern Design */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Staff Card */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600 rounded-3xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-3xl font-bold mb-1">127</h3>
                <p className="text-blue-100 font-medium">Total Staff</p>
              </div>
            </div>

            {/* Active Teachers Card */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-3xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-3xl font-bold mb-1">89</h3>
                <p className="text-emerald-100 font-medium">Active Teachers</p>
              </div>
            </div>

            {/* Admin Staff Card */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-purple-400 to-purple-600 rounded-3xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-3xl font-bold mb-1">38</h3>
                <p className="text-purple-100 font-medium">Admin Staff</p>
              </div>
            </div>

            {/* Departments Card */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-orange-400 to-orange-600 rounded-3xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center mb-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-3xl font-bold mb-1">12</h3>
                <p className="text-orange-100 font-medium">Departments</p>
              </div>
            </div>
          </div>

          {/* Search & Filter Section with Neumorphism */}
          <div className="backdrop-blur-xl bg-white/40 border border-white/30 rounded-3xl p-8 shadow-2xl">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Advanced Search */}
              <div className="flex-1">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000"></div>
                  <div className="relative bg-white rounded-2xl p-1">
                    <div className="flex items-center bg-gray-50 rounded-xl px-4 py-3">
                      <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <input
                        type="text"
                        placeholder="Search by name, department, or role..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="flex-1 bg-transparent outline-none text-gray-700 placeholder-gray-400"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Modern Filters */}
              <div className="flex flex-wrap gap-4">
                <div className="relative">
                  <select
                    value={selectedDepartment}
                    onChange={(e) => setSelectedDepartment(e.target.value)}
                    className="appearance-none bg-white/80 backdrop-blur border border-gray-200 rounded-xl px-4 py-3 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200"
                  >
                    <option value="all">All Departments</option>
                    <option value="mathematics">Mathematics</option>
                    <option value="science">Science</option>
                    <option value="english">English</option>
                    <option value="history">History</option>
                    <option value="administration">Administration</option>
                  </select>
                  <svg className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>

                <div className="relative">
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="appearance-none bg-white/80 backdrop-blur border border-gray-200 rounded-xl px-4 py-3 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="on-leave">On Leave</option>
                  </select>
                  <svg className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>

                <button className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg">
                  <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                  </svg>
                  Apply
                </button>
              </div>
            </div>
          </div>

          {/* Staff Cards Grid - Modern Card Design */}
          <div className="backdrop-blur-xl bg-white/20 border border-white/30 rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Team Members
              </h2>
              <div className="flex gap-3">
                <button className="bg-white/60 hover:bg-white/80 border border-white/30 rounded-xl px-4 py-2 font-medium text-gray-700 transition-all duration-200 hover:scale-105">
                  Export
                </button>
                <button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl px-4 py-2 font-medium transition-all duration-200 hover:scale-105">
                  View All →
                </button>
              </div>
            </div>

            {/* Staff Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { name: 'Dr. Sarah Johnson', email: '<EMAIL>', department: 'Mathematics', role: 'Head of Department', status: 'active', avatar: 'SJ', color: 'from-blue-400 to-blue-600' },
                { name: 'Prof. Michael Chen', email: '<EMAIL>', department: 'Science', role: 'Senior Teacher', status: 'active', avatar: 'MC', color: 'from-emerald-400 to-emerald-600' },
                { name: 'Ms. Emily Davis', email: '<EMAIL>', department: 'English', role: 'Teacher', status: 'on-leave', avatar: 'ED', color: 'from-purple-400 to-purple-600' },
                { name: 'Mr. James Wilson', email: '<EMAIL>', department: 'Administration', role: 'Admin Officer', status: 'active', avatar: 'JW', color: 'from-orange-400 to-orange-600' },
                { name: 'Dr. Lisa Brown', email: '<EMAIL>', department: 'History', role: 'Teacher', status: 'active', avatar: 'LB', color: 'from-pink-400 to-pink-600' },
                { name: 'Prof. David Lee', email: '<EMAIL>', department: 'Physics', role: 'Teacher', status: 'active', avatar: 'DL', color: 'from-cyan-400 to-cyan-600' },
              ].map((staff, index) => (
                <div key={index} className="group relative backdrop-blur-xl bg-white/40 border border-white/30 rounded-2xl p-6 hover:bg-white/60 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br ${staff.color} rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                      {staff.avatar}
                    </div>
                    <div className="flex gap-2">
                      <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-2 bg-white/60 rounded-lg hover:bg-white/80">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                      </button>
                      <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-2 bg-white/60 rounded-lg hover:bg-white/80">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-bold text-gray-900 text-lg">{staff.name}</h3>
                      <p className="text-gray-600 text-sm">{staff.email}</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-800">{staff.role}</p>
                        <p className="text-gray-600 text-sm">{staff.department}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        staff.status === 'active' ? 'bg-emerald-100 text-emerald-800' :
                        staff.status === 'on-leave' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {staff.status === 'active' ? 'Active' : staff.status === 'on-leave' ? 'On Leave' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="xl:col-span-4 space-y-8">
          {/* Department Analytics */}
          <div className="backdrop-blur-xl bg-white/30 border border-white/20 rounded-3xl p-6 shadow-2xl">
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Department Insights
            </h3>
            <div className="space-y-6">
              {[
                { name: 'Mathematics', count: 15, percentage: 75, color: 'from-blue-400 to-blue-600' },
                { name: 'Science', count: 22, percentage: 90, color: 'from-emerald-400 to-emerald-600' },
                { name: 'English', count: 12, percentage: 60, color: 'from-purple-400 to-purple-600' },
                { name: 'Administration', count: 18, percentage: 80, color: 'from-orange-400 to-orange-600' },
              ].map((dept, index) => (
                <div key={index} className="group">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-700">{dept.name}</span>
                    <span className="font-bold text-gray-900">{dept.count}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div 
                      className={`h-full bg-gradient-to-r ${dept.color} rounded-full transition-all duration-1000 group-hover:scale-x-105`}
                      style={{ width: `${dept.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="backdrop-blur-xl bg-white/30 border border-white/20 rounded-3xl p-6 shadow-2xl">
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Quick Actions
            </h3>
            <div className="space-y-3">
              {[
                { title: 'Schedule Meeting', icon: '📅', color: 'from-blue-400 to-blue-600' },
                { title: 'Generate Report', icon: '📊', color: 'from-emerald-400 to-emerald-600' },
                { title: 'Send Announcement', icon: '📢', color: 'from-purple-400 to-purple-600' },
                { title: 'Review Applications', icon: '📋', color: 'from-orange-400 to-orange-600' },
              ].map((action, index) => (
                <button key={index} className={`w-full p-4 bg-gradient-to-r ${action.color} rounded-xl text-white font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg group`}>
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl group-hover:scale-110 transition-transform duration-200">{action.icon}</span>
                    <span>{action.title}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="backdrop-blur-xl bg-white/30 border border-white/20 rounded-3xl p-6 shadow-2xl">
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Upcoming Events
            </h3>
            <div className="space-y-4">
              {[
                { title: 'Staff Meeting', date: 'Tomorrow', time: '10:00 AM', type: 'meeting', color: 'bg-blue-100 text-blue-800' },
                { title: 'Training Workshop', date: 'Dec 15', time: '2:00 PM', type: 'training', color: 'bg-emerald-100 text-emerald-800' },
                { title: 'Performance Review', date: 'Dec 20', time: '9:00 AM', type: 'review', color: 'bg-purple-100 text-purple-800' },
              ].map((event, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-white/40 rounded-xl border border-white/30 hover:bg-white/60 transition-all duration-200 hover:scale-105">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${event.color}`}>
                    {event.type === 'meeting' ? '👥' : event.type === 'training' ? '🎓' : '📝'}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{event.title}</h4>
                    <p className="text-gray-600 text-sm">{event.date} at {event.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
