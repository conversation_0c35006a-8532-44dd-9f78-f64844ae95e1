// src/app/staff-management/page.tsx
'use client';

import { useState } from 'react';

export default function StaffManagementPage() {
  const [activeView, setActiveView] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  return (
    <div className="space-y-5">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white text-sm-app font-medium py-2.5 px-4 rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Import Staff</span>
          </button>
          <button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm-app font-medium py-2.5 px-4 rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Add Staff Member</span>
          </button>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-5">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-3 space-y-5">
          {/* Staff Overview Cards */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-5 hover:shadow-xl transition-all duration-300">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-5">
              {/* Total Staff */}
              <div className="text-center group hover:scale-105 transition-transform duration-200">
                <div className="w-11 h-11 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mx-auto mb-2.5 shadow-sm group-hover:shadow-md transition-shadow duration-200">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-0.5 font-medium tracking-wide">Total Staff</div>
                <div className="text-xl-app font-bold text-slate-900 tracking-tight">127</div>
              </div>

              {/* Active Teachers */}
              <div className="text-center group hover:scale-105 transition-transform duration-200">
                <div className="w-11 h-11 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center mx-auto mb-2.5 shadow-sm group-hover:shadow-md transition-shadow duration-200">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-0.5 font-medium tracking-wide">Active Teachers</div>
                <div className="text-xl-app font-bold text-slate-900 tracking-tight">89</div>
              </div>

              {/* Admin Staff */}
              <div className="text-center group hover:scale-105 transition-transform duration-200">
                <div className="w-11 h-11 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center mx-auto mb-2.5 shadow-sm group-hover:shadow-md transition-shadow duration-200">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-0.5 font-medium tracking-wide">Admin Staff</div>
                <div className="text-xl-app font-bold text-slate-900 tracking-tight">38</div>
              </div>

              {/* Departments */}
              <div className="text-center group hover:scale-105 transition-transform duration-200">
                <div className="w-11 h-11 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center mx-auto mb-2.5 shadow-sm group-hover:shadow-md transition-shadow duration-200">
                  <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="text-xs-app text-slate-500 mb-0.5 font-medium tracking-wide">Departments</div>
                <div className="text-xl-app font-bold text-slate-900 tracking-tight">12</div>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-5 hover:shadow-xl transition-all duration-300">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
              {/* Search Bar */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <input
                    type="text"
                    placeholder="Search staff by name, ID, or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm hover:shadow-md transition-shadow duration-200 text-sm-app"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-3">
                <select
                  value={selectedDepartment}
                  onChange={(e) => setSelectedDepartment(e.target.value)}
                  className="text-sm-app border border-slate-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <option value="all">All Departments</option>
                  <option value="mathematics">Mathematics</option>
                  <option value="science">Science</option>
                  <option value="english">English</option>
                  <option value="history">History</option>
                  <option value="administration">Administration</option>
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="text-sm-app border border-slate-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="on-leave">On Leave</option>
                </select>

                <button className="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white text-sm-app font-medium py-2.5 px-4 rounded-lg flex items-center space-x-2 transition-all duration-200 shadow-md hover:shadow-lg">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                  </svg>
                  <span>Filter</span>
                </button>
              </div>
            </div>
          </div>

          {/* Staff Directory Table */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-5 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-5">
              <h3 className="text-lg-app font-semibold text-slate-900 tracking-tight">Staff Directory</h3>
              <div className="flex items-center space-x-2">
                <button className="text-sm-app text-slate-600 hover:text-slate-700 font-medium hover:underline transition-all duration-200">Export CSV</button>
                <span className="text-slate-300">|</span>
                <button className="text-sm-app text-blue-600 hover:text-blue-700 font-medium hover:underline transition-all duration-200">View All →</button>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto bg-white rounded-lg shadow-sm border border-slate-200/50">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-slate-50 to-slate-100">
                  <tr className="border-b border-slate-200">
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-3 px-4">Staff Member</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-3 px-4">Department</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-3 px-4">Role</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-3 px-4">Status</th>
                    <th className="text-left text-xs-app font-semibold text-slate-600 uppercase tracking-wider py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {[
                    { name: 'Dr. Sarah Johnson', email: '<EMAIL>', department: 'Mathematics', role: 'Head of Department', status: 'active', avatar: 'SJ' },
                    { name: 'Prof. Michael Chen', email: '<EMAIL>', department: 'Science', role: 'Senior Teacher', status: 'active', avatar: 'MC' },
                    { name: 'Ms. Emily Davis', email: '<EMAIL>', department: 'English', role: 'Teacher', status: 'on-leave', avatar: 'ED' },
                    { name: 'Mr. James Wilson', email: '<EMAIL>', department: 'Administration', role: 'Admin Officer', status: 'active', avatar: 'JW' },
                    { name: 'Dr. Lisa Brown', email: '<EMAIL>', department: 'History', role: 'Teacher', status: 'active', avatar: 'LB' },
                  ].map((staff, index) => (
                    <tr key={index} className="hover:bg-slate-50 transition-colors duration-150">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xs-app font-semibold">
                            {staff.avatar}
                          </div>
                          <div>
                            <div className="text-sm-app text-slate-900 font-medium">{staff.name}</div>
                            <div className="text-xs-app text-slate-500">{staff.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm-app text-slate-600 font-medium">{staff.department}</td>
                      <td className="py-3 px-4 text-sm-app text-slate-600">{staff.role}</td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs-app font-medium ${
                          staff.status === 'active' ? 'bg-green-100 text-green-800' :
                          staff.status === 'on-leave' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {staff.status === 'active' ? 'Active' : staff.status === 'on-leave' ? 'On Leave' : 'Inactive'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-700 transition-colors duration-150">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                          </button>
                          <button className="text-slate-600 hover:text-slate-700 transition-colors duration-150">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-5">
          {/* Department Overview */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-5 hover:shadow-xl transition-all duration-300">
            <h3 className="text-lg-app font-semibold text-slate-900 mb-4 tracking-tight">Department Overview</h3>
            <div className="space-y-3.5">
              {/* Mathematics */}
              <div className="group">
                <div className="flex items-center justify-between mb-1.5">
                  <span className="text-sm-app text-slate-700 font-medium">Mathematics</span>
                  <span className="text-sm-app font-semibold text-blue-600">15 Staff</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5 shadow-inner">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 h-2.5 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '75%' }}></div>
                </div>
              </div>

              {/* Science */}
              <div className="group">
                <div className="flex items-center justify-between mb-1.5">
                  <span className="text-sm-app text-slate-700 font-medium">Science</span>
                  <span className="text-sm-app font-semibold text-green-600">22 Staff</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5 shadow-inner">
                  <div className="bg-gradient-to-r from-green-500 to-green-600 h-2.5 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '90%' }}></div>
                </div>
              </div>

              {/* English */}
              <div className="group">
                <div className="flex items-center justify-between mb-1.5">
                  <span className="text-sm-app text-slate-700 font-medium">English</span>
                  <span className="text-sm-app font-semibold text-purple-600">12 Staff</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5 shadow-inner">
                  <div className="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '60%' }}></div>
                </div>
              </div>

              {/* Administration */}
              <div className="group">
                <div className="flex items-center justify-between mb-1.5">
                  <span className="text-sm-app text-slate-700 font-medium">Administration</span>
                  <span className="text-sm-app font-semibold text-orange-600">18 Staff</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5 shadow-inner">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 h-2.5 rounded-full shadow-sm group-hover:shadow-md transition-shadow duration-200" style={{ width: '80%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-gradient-to-br from-white to-slate-50 rounded-xl shadow-lg border border-slate-200/60 p-5 hover:shadow-xl transition-all duration-300">
            <h3 className="text-lg-app font-semibold text-slate-900 mb-4 tracking-tight">Upcoming Events</h3>
            <div className="space-y-3">
              {[
                { title: 'Staff Meeting', date: 'Tomorrow', time: '10:00 AM', type: 'meeting' },
                { title: 'Training Workshop', date: 'Dec 15', time: '2:00 PM', type: 'training' },
                { title: 'Performance Review', date: 'Dec 20', time: '9:00 AM', type: 'review' },
              ].map((event, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-slate-200/50 hover:shadow-sm transition-shadow duration-150">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    event.type === 'meeting' ? 'bg-blue-100 text-blue-600' :
                    event.type === 'training' ? 'bg-green-100 text-green-600' :
                    'bg-purple-100 text-purple-600'
                  }`}>
                    {event.type === 'meeting' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    ) : event.type === 'training' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm-app text-slate-900 font-medium">{event.title}</div>
                    <div className="text-xs-app text-slate-600">{event.date} at {event.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
