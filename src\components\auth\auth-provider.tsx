// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error: string | null }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error: string | null }>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    let subscription: any = null;
    let timeoutId: NodeJS.Timeout;

    // Force loading to false after maximum timeout to prevent infinite loading
    const forceLoadingComplete = () => {
      if (isMounted) {
        setLoading(false);
        // Don't set user to null here - preserve existing user state
        setError(null);
      }
    };

    const initializeAuth = async () => {
      try {
        // Set longer timeout for session verification to prevent premature timeouts
        timeoutId = setTimeout(forceLoadingComplete, 10000);

        // Use enhanced auth check with retry logic
        const { checkAuthStateWithRetry, logAuthDebugInfo } = await import('../../lib/auth-debug');

        // Log debug info in development
        if (process.env.NODE_ENV === 'development') {
          await logAuthDebugInfo('AuthProvider Initialization');
        }

        const isAuthenticated = await checkAuthStateWithRetry(2);

        if (isAuthenticated && isMounted) {
          // Get the user data after successful auth check
          const { verifySession } = await import('../../lib/session');
          const sessionUser = await verifySession();

          if (sessionUser) {
            clearTimeout(timeoutId);
            setUser(sessionUser);
            setError(null);
            setLoading(false);

            // Start session heartbeat to prevent unexpected logouts
            const { startSessionHeartbeat } = await import('../../lib/session-heartbeat');
            startSessionHeartbeat();

            return;
          }
        }

        // If no authentication found after retries
        if (isMounted) {
          clearTimeout(timeoutId);
          setUser(null);
          setError(null);
          setLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (isMounted) {
          clearTimeout(timeoutId);
          // Don't immediately set user to null on error - preserve state
          setError(null);
          setLoading(false);
        }
      }
    };

    // Set up auth state listener with error handling - but don't let it override session-based auth
    const setupAuthListener = async () => {
      try {
        const { onAuthStateChange } = await import('../../lib/supabase-auth-functions');

        const { data } = await onAuthStateChange((supabaseUser) => {
          if (isMounted) {
            // Only update user state if we don't already have a valid session user
            // This prevents Supabase auth state changes from overriding session-based auth
            if (!user || !user.isAuthenticated) {
              setUser(supabaseUser);
            }
            if (loading) {
              setLoading(false);
            }
          }
        });
        subscription = data?.subscription;
      } catch (_error) {
        // Silently fail and ensure loading is false
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Start initialization
    initializeAuth();
    setupAuthListener();

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription?.unsubscribe();
    };
  }, []); // Remove 'user' and 'loading' dependencies to prevent re-initialization

  const signIn = async (email: string, password: string) => {
    try {
      const { signIn: authSignIn } = await import('../../lib/supabase-auth-functions');
      const result = await authSignIn(email, password);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('../../lib/session');
        await createSession(result.user);
        setUser(result.user);

        // Start session heartbeat
        const { startSessionHeartbeat } = await import('../../lib/session-heartbeat');
        startSessionHeartbeat();
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    try {
      const { signUp: authSignUp } = await import('../../lib/supabase-auth-functions');
      const result = await authSignUp(email, password, userData);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('../../lib/session');
        await createSession(result.user);
        setUser(result.user);

        // Start session heartbeat
        const { startSessionHeartbeat } = await import('../../lib/session-heartbeat');
        startSessionHeartbeat();
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signOut = async () => {
    try {
      // Stop session heartbeat
      const { stopSessionHeartbeat } = await import('../../lib/session-heartbeat');
      stopSessionHeartbeat();

      const { signOut: authSignOut } = await import('../../lib/supabase-auth-functions');
      const { deleteSession } = await import('../../lib/session');

      // Sign out from Supabase
      await authSignOut();

      // Delete secure session
      await deleteSession();

      setUser(null);
      setError(null);
    } catch (error) {
      console.error('Sign out error:', error);
      // Force sign out locally even if service fails
      const { stopSessionHeartbeat } = await import('../../lib/session-heartbeat');
      stopSessionHeartbeat();

      const { deleteSession } = await import('../../lib/session');
      await deleteSession();
      setUser(null);
      setError(null);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
