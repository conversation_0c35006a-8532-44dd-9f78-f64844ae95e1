// src/lib/session-heartbeat.ts
'use client';

/**
 * Session heartbeat mechanism to prevent unexpected logouts
 */

let heartbeatInterval: NodeJS.Timeout | null = null;
let isHeartbeatActive = false;

/**
 * Start session heartbeat to keep session alive
 */
export function startSessionHeartbeat(): void {
  // Don't start multiple heartbeats
  if (isHeartbeatActive) {
    return;
  }

  isHeartbeatActive = true;

  // Check and refresh session every 30 minutes
  heartbeatInterval = setInterval(async () => {
    try {
      await refreshSessionIfNeeded();
    } catch (error) {
      console.error('Session heartbeat error:', error);
    }
  }, 30 * 60 * 1000); // 30 minutes

  // Also refresh on page visibility change (when user returns to tab)
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  // Refresh on page focus
  if (typeof window !== 'undefined') {
    window.addEventListener('focus', handleWindowFocus);
  }
}

/**
 * Stop session heartbeat
 */
export function stopSessionHeartbeat(): void {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }

  isHeartbeatActive = false;

  // Remove event listeners
  if (typeof document !== 'undefined') {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  }

  if (typeof window !== 'undefined') {
    window.removeEventListener('focus', handleWindowFocus);
  }
}

/**
 * Handle page visibility change
 */
async function handleVisibilityChange(): Promise<void> {
  if (document.visibilityState === 'visible') {
    try {
      await refreshSessionIfNeeded();
    } catch (error) {
      console.error('Visibility change session refresh error:', error);
    }
  }
}

/**
 * Handle window focus
 */
async function handleWindowFocus(): Promise<void> {
  try {
    await refreshSessionIfNeeded();
  } catch (error) {
    console.error('Window focus session refresh error:', error);
  }
}

/**
 * Refresh session if it's close to expiring
 */
async function refreshSessionIfNeeded(): Promise<void> {
  try {
    // Import session functions dynamically to avoid SSR issues
    const { getSession, refreshSession } = await import('./session');
    
    const session = await getSession();
    
    if (!session) {
      return;
    }

    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
    const refreshThreshold = 2 * 60 * 60 * 1000; // Refresh if less than 2 hours remaining

    if (timeUntilExpiry < refreshThreshold && timeUntilExpiry > 0) {
      console.log('Refreshing session due to heartbeat check');
      await refreshSession();
    }
  } catch (error) {
    console.error('Session refresh check failed:', error);
  }
}

/**
 * Check if session heartbeat is active
 */
export function isSessionHeartbeatActive(): boolean {
  return isHeartbeatActive;
}

/**
 * Force session refresh
 */
export async function forceSessionRefresh(): Promise<boolean> {
  try {
    const { refreshSession } = await import('./session');
    await refreshSession();
    return true;
  } catch (error) {
    console.error('Force session refresh failed:', error);
    return false;
  }
}

/**
 * Get session status information
 */
export async function getSessionStatus(): Promise<{
  exists: boolean;
  valid: boolean;
  expiresAt: string | null;
  timeUntilExpiry: number | null;
  needsRefresh: boolean;
}> {
  try {
    const { getSession, verifySession } = await import('./session');
    
    const session = await getSession();
    const user = await verifySession();
    
    if (!session) {
      return {
        exists: false,
        valid: false,
        expiresAt: null,
        timeUntilExpiry: null,
        needsRefresh: false,
      };
    }

    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
    const refreshThreshold = 2 * 60 * 60 * 1000; // 2 hours

    return {
      exists: true,
      valid: !!user,
      expiresAt: session.expiresAt.toString(),
      timeUntilExpiry,
      needsRefresh: timeUntilExpiry < refreshThreshold && timeUntilExpiry > 0,
    };
  } catch (error) {
    console.error('Get session status failed:', error);
    return {
      exists: false,
      valid: false,
      expiresAt: null,
      timeUntilExpiry: null,
      needsRefresh: false,
    };
  }
}
